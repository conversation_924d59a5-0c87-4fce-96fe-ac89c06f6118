import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { useState, useEffect } from "react";
import Loading from "./components/Loading";
import Index from "./pages/Index";
import About from "./pages/About";
import News from "./pages/News";
import Fixtures from "./pages/Fixtures";
import Players from "./pages/Players";
import Gallery from "./pages/Gallery";
import FanZone from "./pages/FanZone";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => {
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Check if user has already seen the loading screen in this session
    const hasSeenLoading = sessionStorage.getItem('hasSeenLoading');

    if (!hasSeenLoading) {
      setIsLoading(true);
    }
  }, []);

  const handleLoadingComplete = () => {
    setIsLoading(false);
    // Mark that user has seen the loading screen in this session
    sessionStorage.setItem('hasSeenLoading', 'true');
  };

  if (isLoading) {
    return <Loading onLoadingComplete={handleLoadingComplete} />;
  }

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/about" element={<About />} />
            <Route path="/news" element={<News />} />
            <Route path="/fixtures" element={<Fixtures />} />
            <Route path="/players" element={<Players />} />
            <Route path="/gallery" element={<Gallery />} />
            <Route path="/fan-zone" element={<FanZone />} />
            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;
