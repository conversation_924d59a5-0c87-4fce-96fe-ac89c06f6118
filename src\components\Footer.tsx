import { Mail, Phone, MapPin } from "lucide-react";
import { motion } from "framer-motion";

const Footer = () => {
  return (
    <footer className="bg-tm-navy text-tm-white py-8 md:py-12 mt-8 md:mt-12">
      <div className="container mx-auto px-4">
        {/* Mobile: Stacked Layout | Desktop: 3-Column Grid */}
        <div className="block md:hidden mb-8">
          {/* Mobile Layout - Stacked */}
          {/* Club Info Section */}
          <div className="text-center mb-6">
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="flex items-center justify-center space-x-3 mb-4"
            >
              <motion.img
                src="/Crown_F.C_Nigeria_logo.svg.png"
                alt="Crown Football Club Nigeria Logo"
                className="w-10 h-10"
                whileHover={{
                  rotate: 360,
                  scale: 1.1
                }}
                transition={{ duration: 0.6 }}
              />
              <div>
                <h3 className="text-base font-bold">Crown FC</h3>
                <p className="text-tm-blue text-xs">Ogbomoso</p>
              </div>
            </motion.div>
            <p className="text-xs text-tm-gray leading-relaxed">
              Founded in 1994, Crown Football Club represents excellence in Nigerian football.
            </p>
          </div>



          {/* Contact Info */}
          <div className="text-center mb-6">
            <h4 className="text-sm font-semibold mb-3">Contact Us</h4>
            <div className="space-y-2 text-xs">
              <div className="flex items-center justify-center space-x-2">
                <MapPin size={12} className="text-tm-blue flex-shrink-0" />
                <span>Soun Stadium Ogbomosho, Nigeria</span>
              </div>
              <div className="flex items-center justify-center space-x-2">
                <Phone size={12} className="text-tm-blue flex-shrink-0" />
                <a href="tel:+2348064232555" className="hover:text-tm-blue transition-colors">+2348064232555</a>
              </div>
              <div className="flex items-center justify-center space-x-2">
                <Mail size={12} className="text-tm-blue flex-shrink-0" />
                <a href="mailto:<EMAIL>" className="hover:text-tm-blue transition-colors"><EMAIL></a>
              </div>
            </div>
          </div>

          {/* Social Media */}
          <div className="text-center">
            <h4 className="text-sm font-semibold mb-3">Follow Us</h4>
            <div className="flex justify-center mb-3">
              <a
                href="https://www.facebook.com/share/g/1BEVwvt6ET/?mibextid=wwXIfr"
                target="_blank"
                rel="noopener noreferrer"
                className="hover:text-tm-blue transition-colors"
                title="Facebook"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
            </div>
            <p className="text-xs text-tm-gray">
              Stay updated with latest news
            </p>
          </div>
        </div>

        {/* Desktop Layout - 2-Column Grid */}
        <div className="hidden md:grid md:grid-cols-2 gap-8 mb-8">
          {/* Club Info */}
          <motion.div
            className="text-left"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center space-x-3 mb-4">
              <motion.img
                src="/Crown_F.C_Nigeria_logo.svg.png"
                alt="Crown Football Club Nigeria Logo"
                className="w-12 h-12"
                whileHover={{
                  rotate: 360,
                  scale: 1.1
                }}
                transition={{ duration: 0.6 }}
              />
              <div>
                <h3 className="text-xl font-bold">Crown FC</h3>
                <p className="text-tm-blue text-sm">Ogbomoso</p>
              </div>
            </div>
            <p className="text-sm text-tm-gray leading-relaxed">
              Founded in 1994, Crown Football Club represents excellence in Nigerian football,
              competing with pride and determination in the Nigeria National League.
            </p>
          </motion.div>



          {/* Contact Info & Social Media Combined */}
          <div className="text-left">
            <h4 className="text-lg font-semibold mb-4">Contact & Follow</h4>
            <div className="space-y-3 text-sm mb-6">
              <div className="flex items-center space-x-2">
                <MapPin size={16} className="text-tm-blue flex-shrink-0" />
                <span>Soun Stadium Ogbomosho, Nigeria</span>
              </div>
              <div className="flex items-center space-x-2">
                <Phone size={16} className="text-tm-blue flex-shrink-0" />
                <a href="tel:+2348064232555" className="hover:text-tm-blue transition-colors">+2348064232555</a>
              </div>
              <div className="flex items-center space-x-2">
                <Mail size={16} className="text-tm-blue flex-shrink-0" />
                <a href="mailto:<EMAIL>" className="hover:text-tm-blue transition-colors"><EMAIL></a>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <a
                href="https://www.facebook.com/share/g/1BEVwvt6ET/?mibextid=wwXIfr"
                target="_blank"
                rel="noopener noreferrer"
                className="hover:text-tm-blue transition-colors"
                title="Facebook"
              >
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
              <span className="text-xs text-tm-gray">Follow us on Facebook</span>
            </div>
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="border-t border-tm-blue/30 pt-6">
          <div className="text-center">
            <p className="text-sm text-tm-gray">
              &copy; 2025 Crown Football Club. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
