import { motion } from "framer-motion";
import { useEffect, useState } from "react";

interface LoadingProps {
  onLoadingComplete: () => void;
}

const Loading = ({ onLoadingComplete }: LoadingProps) => {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 100) {
          clearInterval(timer);
          setTimeout(onLoadingComplete, 500); // Small delay after reaching 100%
          return 100;
        }
        return prev + (100 / 70); // 7 seconds = 7000ms, update every 100ms
      });
    }, 100);

    return () => clearInterval(timer);
  }, [onLoadingComplete]);

  return (
    <div className="fixed inset-0 bg-tm-navy flex items-center justify-center z-50">
      <div className="text-center">
        {/* Logo with animation */}
        <motion.div
          initial={{ scale: 0.5, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 1, ease: "easeOut" }}
          className="mb-8"
        >
          <motion.img
            src="/Crown_F.C_Nigeria_logo.svg.png"
            alt="Crown Football Club Nigeria Logo"
            className="w-32 h-32 mx-auto"
            animate={{ 
              rotate: [0, 360],
              scale: [1, 1.1, 1]
            }}
            transition={{ 
              rotate: { duration: 3, repeat: Infinity, ease: "linear" },
              scale: { duration: 2, repeat: Infinity, ease: "easeInOut" }
            }}
          />
        </motion.div>

        {/* Club Name */}
        <motion.div
          initial={{ y: 30, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.5, duration: 0.8 }}
          className="mb-8"
        >
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-2">
            Crown Football Club
          </h1>
          <p className="text-xl md:text-2xl text-tm-blue font-semibold">
            Ogbomoso
          </p>
          <p className="text-lg text-tm-gray mt-2">
            Home of Champions
          </p>
        </motion.div>

        {/* Loading Progress Bar */}
        <motion.div
          initial={{ width: 0, opacity: 0 }}
          animate={{ width: "100%", opacity: 1 }}
          transition={{ delay: 1, duration: 0.5 }}
          className="w-15 mx-auto"
        >
          <div className="bg-tm-gray rounded-full h-2 mb-4">
            <motion.div
              className="bg-tm-blue h-2 rounded-full"
              style={{ width: `${progress}%` }}
              transition={{ duration: 0.1 }}
            />
          </div>
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.5 }}
            className="text-tm-gray text-sm"
          >
            Loading... {Math.round(progress)}%
          </motion.p>
        </motion.div>

        {/* Welcome Message */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 2 }}
          className="mt-8"
        >
          <p className="text-tm-gray text-sm">
            Welcome to the official website
          </p>
        </motion.div>
      </div>
    </div>
  );
};

export default Loading;
