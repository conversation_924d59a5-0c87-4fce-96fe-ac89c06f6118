import { motion } from "framer-motion";
import { useEffect, useState } from "react";

interface PageLoadingProps {
  onLoadingComplete: () => void;
}

const PageLoading = ({ onLoadingComplete }: PageLoadingProps) => {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 100) {
          clearInterval(timer);
          setTimeout(onLoadingComplete, 200); // Small delay after reaching 100%
          return 100;
        }
        return prev + (100 / 30); // 3 seconds = 3000ms, update every 100ms
      });
    }, 100);

    return () => clearInterval(timer);
  }, [onLoadingComplete]);

  return (
    <div className="fixed inset-0 bg-white/90 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="text-center">
        {/* Crown FC Logo */}
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5, ease: "easeOut" }}
          className="mb-6"
        >
          <motion.img
            src="/Crown_F.C_Nigeria_logo.svg.png"
            alt="Crown Football Club Nigeria Logo"
            className="w-16 h-16 mx-auto"
            animate={{ 
              rotate: [0, 360]
            }}
            transition={{ 
              rotate: { duration: 2, repeat: Infinity, ease: "linear" }
            }}
          />
        </motion.div>

        {/* Loading Progress Bar */}
        <motion.div
          initial={{ width: 0, opacity: 0 }}
          animate={{ width: "100%", opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.3 }}
          className="w-8 md:w-12 mx-auto"
        >
          <div className="bg-tm-gray/30 rounded-full h-1 mb-3">
            <motion.div
              className="bg-tm-blue h-1 rounded-full"
              style={{ width: `${progress}%` }}
              transition={{ duration: 0.1 }}
            />
          </div>
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="text-tm-navy text-xs font-medium"
          >
            Loading...
          </motion.p>
        </motion.div>
      </div>
    </div>
  );
};

export default PageLoading;
