import Header from "@/components/Header";
import Footer from "@/components/Footer";
import Breadcrumb from "@/components/Breadcrumb";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Facebook, Mail, Phone, MapPin, Heart, MessageCircle } from "lucide-react";

const FanZone = () => {
  return (
    <>
      <Header />
      <Breadcrumb 
        items={[
          { label: "Home", href: "/" },
          { label: "Fan Zone", href: "/fan-zone" }
        ]}
      />
      
      <div className="container mx-auto px-4 py-6">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-2xl md:text-3xl font-bold text-tm-navy mb-8">Fan Zone</h1>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Contact Form */}
            <Card>
              <CardHeader className="bg-tm-navy text-tm-white">
                <h2 className="font-bold flex items-center gap-2">
                  <MessageCircle className="h-5 w-5" />
                  Contact Us
                </h2>
              </CardHeader>
              <CardContent className="p-6">
                <form className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-tm-navy mb-2">
                        First Name
                      </label>
                      <Input placeholder="Enter your first name" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-tm-navy mb-2">
                        Last Name
                      </label>
                      <Input placeholder="Enter your last name" />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-tm-navy mb-2">
                      Email Address
                    </label>
                    <Input type="email" placeholder="Enter your email" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-tm-navy mb-2">
                      Phone Number
                    </label>
                    <Input type="tel" placeholder="Enter your phone number" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-tm-navy mb-2">
                      Subject
                    </label>
                    <Input placeholder="Enter message subject" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-tm-navy mb-2">
                      Message
                    </label>
                    <Textarea placeholder="Enter your message" rows={4} />
                  </div>
                  <Button className="w-full bg-tm-blue hover:bg-tm-navy">
                    Send Message
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* Club Information */}
            <div className="space-y-6">
              {/* Contact Information */}
              <Card>
                <CardHeader className="bg-tm-blue text-tm-white">
                  <h2 className="font-bold">Contact Information</h2>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <MapPin className="h-5 w-5 text-tm-blue" />
                      <div>
                        <div className="font-medium text-tm-navy">Soun Stadium Ogbomosho</div>
                      
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Phone className="h-5 w-5 text-tm-blue" />
                      <div>
                        <div className="font-medium text-tm-navy">+2348064232555</div>
                        <div className="text-sm text-tm-dark-gray">Main Office</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Mail className="h-5 w-5 text-tm-blue" />
                      <div>
                        <div className="font-medium text-tm-navy"><EMAIL></div>
                        <div className="text-sm text-tm-dark-gray">General Inquiries</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Social Media */}
              <Card>
                <CardHeader className="bg-tm-navy text-tm-white">
                  <h2 className="font-bold">Follow Us</h2>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="grid grid-cols-1 gap-4">
                    <Button
                      variant="outline"
                      className="flex items-center gap-2 hover:bg-blue-50"
                      onClick={() => window.open('https://www.facebook.com/share/g/1BEVwvt6ET/?mibextid=wwXIfr', '_blank')}
                    >
                      <Facebook className="h-4 w-4 text-blue-600" />
                      Facebook
                    </Button>
                  </div>
                  <div className="mt-4 text-center">
                    <p className="text-sm text-tm-dark-gray">
                      Stay connected with Crown FC for latest updates, match highlights, and exclusive content!
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>



          {/* Fan Wall */}
          <Card className="mt-8">
            <CardHeader className="bg-tm-navy text-tm-white">
              <h2 className="font-bold flex items-center gap-2">
                <Heart className="h-5 w-5" />
                Fan Wall
              </h2>
            </CardHeader>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="bg-tm-gray rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-8 h-8 bg-tm-blue rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-bold">IO</span>
                    </div>
                    <div>
                      <div className="font-medium text-tm-navy">Ibrahim Obayomi</div>
                      <div className="text-xs text-tm-dark-gray">Fan since 2018</div>
                    </div>
                  </div>
                  <p className="text-sm text-tm-dark-gray">
                    "Crown FC is more than a team, it's a family! Love supporting our boys every match day!"
                  </p>
                  <div className="mt-2">
                    <Badge variant="outline" className="text-xs">
                      Season Ticket Holder
                    </Badge>
                  </div>
                </div>
                
                <div className="bg-tm-gray rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-8 h-8 bg-tm-blue rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-bold">PG</span>
                    </div>
                    <div>
                      <div className="font-medium text-tm-navy">Peter Godly Michael</div>
                      <div className="text-xs text-tm-dark-gray">Fan since 2000</div>
                    </div>
                  </div>
                  <p className="text-sm text-tm-dark-gray">
                    "The atmosphere at Crown Stadium is incredible! Best fans in the league! 💙"
                  </p>
                  <div className="mt-2">
                    <Badge variant="outline" className="text-xs">
                      Youth Supporter
                    </Badge>
                  </div>
                </div>
                
                <div className="bg-tm-gray rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-8 h-8 bg-tm-blue rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-bold">AO</span>
                    </div>
                    <div>
                      <div className="font-medium text-tm-navy">Alexander Olasumbo Lawal</div>
                      <div className="text-xs text-tm-dark-gray">Fan since 2015</div>
                    </div>
                  </div>
                  <p className="text-sm text-tm-dark-gray">
                    "Crown FC forever! Can't wait for the next home game. The team is looking strong this season!"
                  </p>
                  <div className="mt-2">
                    <Badge variant="outline" className="text-xs">
                      Player
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Footer />
    </>
  );
};

export default FanZone;