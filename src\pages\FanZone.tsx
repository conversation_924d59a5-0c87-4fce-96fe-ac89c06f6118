import Header from "@/components/Header";
import Footer from "@/components/Footer";
import Breadcrumb from "@/components/Breadcrumb";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Mail, Phone, MapPin, Heart, MessageCircle, Users } from "lucide-react";
import { useState } from "react";
import { useToast } from "@/components/ui/use-toast";
import PageLoading from "@/components/PageLoading";

const FanZone = () => {
  const [isPageLoading, setIsPageLoading] = useState(true);
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.firstName || !formData.lastName || !formData.email || !formData.message) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    // Create email content
    const emailSubject = formData.subject || "Contact Form Submission from Crown FC Website";
    const emailBody = `
Dear Crown Football Club Team,

You have received a new message from your website contact form:

Name: ${formData.firstName} ${formData.lastName}
Email: ${formData.email}
Phone: ${formData.phone || 'Not provided'}
Subject: ${formData.subject || 'General Inquiry'}

Message:
${formData.message}

Best regards,
${formData.firstName} ${formData.lastName}
    `.trim();

    // Create mailto link
    const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent(emailSubject)}&body=${encodeURIComponent(emailBody)}`;

    // Open email client
    window.location.href = mailtoLink;

    // Show success message
    toast({
      title: "Email Client Opened",
      description: "Your email client has been opened with the message. Please send the email to complete your inquiry.",
    });

    // Reset form
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      subject: '',
      message: ''
    });
  };

  const handlePageLoadingComplete = () => {
    setIsPageLoading(false);
  };

  if (isPageLoading) {
    return <PageLoading onLoadingComplete={handlePageLoadingComplete} />;
  }

  return (
    <>
      <Header />
      <Breadcrumb 
        items={[
          { label: "Home", href: "/" },
          { label: "Fan Zone", href: "/fan-zone" }
        ]}
      />
      
      <div className="min-h-screen bg-gradient-to-br from-slate-50/50 to-blue-50/30">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-7xl mx-auto">
          <h1 className="text-2xl md:text-3xl font-bold text-tm-navy mb-8">Fan Zone</h1>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Contact Form */}
            <Card className="border-0 shadow-2xl bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-100/40 backdrop-blur-sm">
              <CardHeader className="bg-gradient-to-r from-tm-navy via-tm-blue to-tm-navy text-white relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-tm-navy/90 via-tm-blue/80 to-tm-navy/90"></div>
                <div className="absolute inset-0 opacity-20">
                  <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-white/5"></div>
                  <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_30%_20%,rgba(255,255,255,0.1),transparent_50%)]"></div>
                </div>
                <h2 className="font-bold text-xl flex items-center gap-2 relative z-10">
                  <span className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse"></span>
                  <MessageCircle className="h-5 w-5" />
                  Contact Us
                  <span className="text-sm font-normal opacity-80 ml-2">(Get in Touch)</span>
                </h2>
              </CardHeader>
              <CardContent className="p-6 bg-gradient-to-br from-white/80 via-slate-50/60 to-blue-50/40 backdrop-blur-sm">
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-tm-navy mb-2">
                        First Name *
                      </label>
                      <Input
                        name="firstName"
                        value={formData.firstName}
                        onChange={handleInputChange}
                        placeholder="Enter your first name"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-tm-navy mb-2">
                        Last Name *
                      </label>
                      <Input
                        name="lastName"
                        value={formData.lastName}
                        onChange={handleInputChange}
                        placeholder="Enter your last name"
                        required
                      />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-tm-navy mb-2">
                      Email Address *
                    </label>
                    <Input
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="Enter your email"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-tm-navy mb-2">
                      Phone Number
                    </label>
                    <Input
                      name="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={handleInputChange}
                      placeholder="Enter your phone number"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-tm-navy mb-2">
                      Subject
                    </label>
                    <Input
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      placeholder="Enter message subject"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-tm-navy mb-2">
                      Message *
                    </label>
                    <Textarea
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      placeholder="Enter your message"
                      rows={4}
                      required
                    />
                  </div>
                  <Button type="submit" className="w-full bg-tm-blue hover:bg-tm-navy transition-colors duration-200">
                    Send Message
                  </Button>
                  <p className="text-xs text-tm-dark-gray text-center mt-2">
                    * Required fields. Your email client will open to send the message.
                  </p>
                </form>
              </CardContent>
            </Card>

            {/* Club Information */}
            <div className="space-y-6">
              {/* Contact Information */}
              <Card>
                <CardHeader className="bg-tm-blue text-tm-white">
                  <h2 className="font-bold">Contact Information</h2>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <MapPin className="h-5 w-5 text-tm-blue" />
                      <div>
                        <div className="font-medium text-tm-navy">Soun Stadium Ogbomosho</div>
                      
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Phone className="h-5 w-5 text-tm-blue" />
                      <div>
                        <div className="font-medium text-tm-navy">+2348064232555</div>
                        <div className="text-sm text-tm-dark-gray">Main Office</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Mail className="h-5 w-5 text-tm-blue" />
                      <div>
                        <div className="font-medium text-tm-navy"><EMAIL></div>
                        <div className="text-sm text-tm-dark-gray">General Inquiries</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Social Media */}
              <Card className="border-0 shadow-2xl bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-100/40 backdrop-blur-sm">
                <CardHeader className="bg-gradient-to-r from-tm-navy via-tm-blue to-tm-navy text-white relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-tm-navy/90 via-tm-blue/80 to-tm-navy/90"></div>
                  <div className="absolute inset-0 opacity-20">
                    <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-white/5"></div>
                    <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_30%_20%,rgba(255,255,255,0.1),transparent_50%)]"></div>
                  </div>
                  <h2 className="font-bold text-xl flex items-center gap-2 relative z-10">
                    <span className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></span>
                    Follow Us
                    <span className="text-sm font-normal opacity-80 ml-2">(Stay Connected)</span>
                  </h2>
                </CardHeader>
                <CardContent className="p-6 bg-gradient-to-br from-white/80 via-slate-50/60 to-blue-50/40 backdrop-blur-sm">
                  <div className="grid grid-cols-1 gap-4">
                    <Button
                      variant="outline"
                      className="flex items-center gap-2 hover:bg-blue-50"
                      onClick={() => window.open('https://www.facebook.com/share/g/1BEVwvt6ET/?mibextid=wwXIfr', '_blank')}
                    >
                      <Users className="h-4 w-4 text-blue-600" />
                      Facebook
                    </Button>
                  </div>
                  <div className="mt-4 text-center">
                    <p className="text-sm text-tm-dark-gray">
                      Stay connected with Crown FC for latest updates, match highlights, and exclusive content!
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>



          {/* Fan Wall */}
          <Card className="mt-8 border-0 shadow-2xl bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-100/40 backdrop-blur-sm">
            <CardHeader className="bg-gradient-to-r from-tm-navy via-tm-blue to-tm-navy text-white relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-tm-navy/90 via-tm-blue/80 to-tm-navy/90"></div>
              <div className="absolute inset-0 opacity-20">
                <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-white/5"></div>
                <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_30%_20%,rgba(255,255,255,0.1),transparent_50%)]"></div>
              </div>
              <h2 className="font-bold text-xl flex items-center gap-2 relative z-10">
                <span className="w-2 h-2 bg-rose-400 rounded-full animate-pulse"></span>
                <Heart className="h-5 w-5" />
                Fan Wall
                <span className="text-sm font-normal opacity-80 ml-2">(Fan Messages)</span>
              </h2>
            </CardHeader>
            <CardContent className="p-6 bg-gradient-to-br from-white/80 via-slate-50/60 to-blue-50/40 backdrop-blur-sm">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="bg-tm-gray rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-8 h-8 bg-tm-blue rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-bold">IO</span>
                    </div>
                    <div>
                      <div className="font-medium text-tm-navy">Ibrahim Obayomi</div>
                      <div className="text-xs text-tm-dark-gray">Fan since 2018</div>
                    </div>
                  </div>
                  <p className="text-sm text-tm-dark-gray">
                    "Crown FC is more than a team, it's a family! Love supporting our boys every match day!"
                  </p>
                  <div className="mt-2">
                    <Badge variant="outline" className="text-xs">
                      Season Ticket Holder
                    </Badge>
                  </div>
                </div>
                
                <div className="bg-tm-gray rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-8 h-8 bg-tm-blue rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-bold">PG</span>
                    </div>
                    <div>
                      <div className="font-medium text-tm-navy">Peter Godly Michael</div>
                      <div className="text-xs text-tm-dark-gray">Fan since 2000</div>
                    </div>
                  </div>
                  <p className="text-sm text-tm-dark-gray">
                    "The atmosphere at Crown Stadium is incredible! Best fans in the league! 💙"
                  </p>
                  <div className="mt-2">
                    <Badge variant="outline" className="text-xs">
                      Youth Supporter
                    </Badge>
                  </div>
                </div>
                
                <div className="bg-tm-gray rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-8 h-8 bg-tm-blue rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-bold">AO</span>
                    </div>
                    <div>
                      <div className="font-medium text-tm-navy">Alexander Olasumbo Lawal</div>
                      <div className="text-xs text-tm-dark-gray">Fan since 2015</div>
                    </div>
                  </div>
                  <p className="text-sm text-tm-dark-gray">
                    "Crown FC forever! Can't wait for the next home game. The team is looking strong this season!"
                  </p>
                  <div className="mt-2">
                    <Badge variant="outline" className="text-xs">
                      Player
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          </div>
        </div>
      </div>

      <Footer />
    </>
  );
};

export default FanZone;