import Header from "@/components/Header";
import Footer from "@/components/Footer";
import Breadcrumb from "@/components/Breadcrumb";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Camera, Video, X, ZoomIn } from "lucide-react";
import { useState } from "react";

const Gallery = () => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  const openImageModal = (imageSrc: string) => {
    setSelectedImage(imageSrc);
  };

  const closeImageModal = () => {
    setSelectedImage(null);
  };

  const photos = [
    {
      id: 1,
      title: "Team Unity",
      description: "Official team photo showcasing our unity and strength",
      image: "/team.jpeg",
      category: "team",
      date: "2025-01-15"
    },
    {
      id: 2,
      title: "Team photo after match",
      description: "Crown FC at our magnificent home stadium",
      image: "/1.jpeg",
      category: "matches",
      date: "2025-01-14"
    },
    {
      id: 3,
      title: "Crown FC Heritage",
      description: "Celebrating our rich football heritage and tradition",
      image: "/hero.jpeg",
      category: "team",
      date: "2025-01-10"
    },
    {
      id: 4,
      title: "Victory Celebration",
      description: "Team photo after winning match",
      image: "/2.jpeg",
      category: "matches",
      date: "2025-01-08"
    },
    {
      id: 5,
      title: "Home Ground",
      description: "Our players in action at the home stadium",
      image: "/3.jpeg",
      category: "matches",
      date: "2025-01-05"
    },
    {
      id: 6,
      title: "Crown FC Pride",
      description: "Coaching session",
      image: "/4.jpeg",
      category: "coach",
      date: "2025-01-03"
    }
  ];

  const videos = [
    {
      id: 1,
      title: "Match Highlights - Crown FC vs Thunder FC",
      description: "Best moments from our latest victory",
      duration: "3:45",
      views: "2.1K",
      date: "2025-01-15"
    },
    {
      id: 2,
      title: "Player Interview - Wasiu Alalade",
      description: "Exclusive interview with our star striker",
      duration: "5:23",
      views: "1.8K",
      date: "2025-01-12"
    },
    {
      id: 3,
      title: "Training Ground Tour",
      description: "Behind the scenes at Crown FC training facility",
      duration: "4:12",
      views: "1.5K",
      date: "2025-01-10"
    },
    {
      id: 4,
      title: "Fan Chants & Atmosphere",
      description: "Amazing atmosphere during home matches",
      duration: "2:38",
      views: "3.2K",
      date: "2025-01-08"
    }
  ];

  return (
    <>
      <Header />
      <Breadcrumb 
        items={[
          { label: "Home", href: "/" },
          { label: "Gallery", href: "/gallery" }
        ]}
      />
      
      <div className="min-h-screen bg-gradient-to-br from-slate-50/50 to-blue-50/30">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-7xl mx-auto">
          <h1 className="text-2xl md:text-3xl font-bold text-tm-navy mb-8">Gallery</h1>
          
          <Tabs defaultValue="photos" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="photos">Photos</TabsTrigger>
              <TabsTrigger value="videos">Videos</TabsTrigger>
            </TabsList>
            
            <TabsContent value="photos" className="space-y-6">
              {/* Photo Categories */}
              <Card className="border-0 shadow-2xl bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-100/40 backdrop-blur-sm">
                <CardHeader className="bg-gradient-to-r from-tm-navy via-tm-blue to-tm-navy text-white relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-tm-navy/90 via-tm-blue/80 to-tm-navy/90"></div>
                  <div className="absolute inset-0 opacity-20">
                    <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-white/5"></div>
                    <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_30%_20%,rgba(255,255,255,0.1),transparent_50%)]"></div>
                  </div>
                  <h2 className="font-bold text-xl flex items-center gap-2 relative z-10">
                    <span className="w-2 h-2 bg-pink-400 rounded-full animate-pulse"></span>
                    <Camera className="h-5 w-5" />
                    Photo Gallery
                    <span className="text-sm font-normal opacity-80 ml-2">(Latest Captures)</span>
                  </h2>
                </CardHeader>
                <CardContent className="p-6 bg-gradient-to-br from-white/80 via-slate-50/60 to-blue-50/40 backdrop-blur-sm">
                  
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {photos.map((photo) => (
                      <div key={photo.id} className="group cursor-pointer">
                        <div
                          className="relative rounded-lg h-48 mb-3 overflow-hidden group-hover:shadow-lg transition-shadow"
                          onClick={() => openImageModal(photo.image)}
                        >
                          <img
                            src={photo.image}
                            alt={photo.title}
                            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-tm-navy/60 via-transparent to-transparent group-hover:from-tm-navy/80 transition-all duration-300"></div>
                          <div className="absolute bottom-2 left-2">
                            <Camera className="h-5 w-5 text-white" />
                          </div>
                          <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <ZoomIn className="h-5 w-5 text-white" />
                          </div>
                        </div>
                        <div className="space-y-2">
                          <h3 className="font-bold text-tm-navy group-hover:text-tm-blue transition-colors">
                            {photo.title}
                          </h3>
                          <p className="text-sm text-tm-dark-gray">
                            {photo.description}
                          </p>
                          <div className="flex items-center justify-between">
                            <Badge variant="outline" className="text-xs">
                              {photo.category}
                            </Badge>
                            <span className="text-xs text-tm-dark-gray">
                              {new Date(photo.date).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="videos" className="space-y-6">
              <Card className="border-0 shadow-2xl bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-100/40 backdrop-blur-sm">
                <CardHeader className="bg-gradient-to-r from-tm-navy via-tm-blue to-tm-navy text-white relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-tm-navy/90 via-tm-blue/80 to-tm-navy/90"></div>
                  <div className="absolute inset-0 opacity-20">
                    <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-white/5"></div>
                    <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_30%_20%,rgba(255,255,255,0.1),transparent_50%)]"></div>
                  </div>
                  <h2 className="font-bold text-xl flex items-center gap-2 relative z-10">
                    <span className="w-2 h-2 bg-red-400 rounded-full animate-pulse"></span>
                    <Video className="h-5 w-5" />
                    Video Gallery
                    <span className="text-sm font-normal opacity-80 ml-2">(Match Highlights)</span>
                  </h2>
                </CardHeader>
                <CardContent className="p-6 bg-gradient-to-br from-white/80 via-slate-50/60 to-blue-50/40 backdrop-blur-sm">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {videos.map((video) => (
                      <div key={video.id} className="group cursor-pointer">
                        <div className="bg-tm-gray rounded-lg h-48 flex items-center justify-center mb-3 group-hover:shadow-lg transition-shadow relative">
                          <Video className="h-12 w-12 text-tm-blue" />
                          <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs">
                            {video.duration}
                          </div>
                        </div>
                        <div className="space-y-2">
                          <h3 className="font-bold text-tm-navy group-hover:text-tm-blue transition-colors">
                            {video.title}
                          </h3>
                          <p className="text-sm text-tm-dark-gray">
                            {video.description}
                          </p>
                          <div className="flex items-center justify-between">
                            <span className="text-xs text-tm-dark-gray">
                              {video.views} views
                            </span>
                            <span className="text-xs text-tm-dark-gray">
                              {new Date(video.date).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Stats */}
         
          </div>
        </div>
      </div>

      {/* Image Modal */}
      {selectedImage && (
        <div
          className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-50 p-4"
          onClick={closeImageModal}
        >
          <div className="relative max-w-4xl max-h-full">
            <button
              onClick={closeImageModal}
              className="absolute top-4 right-4 z-10 bg-white/20 hover:bg-white/30 rounded-full p-2 transition-colors"
            >
              <X className="h-6 w-6 text-white" />
            </button>
            <img
              src={selectedImage}
              alt="Gallery Image"
              className="max-w-full max-h-[90vh] object-contain rounded-lg shadow-2xl"
              onClick={(e) => e.stopPropagation()}
            />
          </div>
        </div>
      )}

      <Footer />
    </>
  );
};

export default Gallery;