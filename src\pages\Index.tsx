import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, Play, Users, Trophy, Target } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { useState, useEffect } from "react";

const Index = () => {
  const [currentSlide, setCurrentSlide] = useState(0);

  // Hero carousel data
  const heroSlides = [
    {
      id: 1,
      image: "/hero.jpeg",
      title: "Crown Football Club",
      subtitle: "Ogbomoso",
      description: "Excellence • Passion • Victory"
    },
    {
      id: 2,
      image: "/1.jpeg",
      title: "Crown Football Club",
      subtitle: "Ogbomoso",
      description: "Home of Champions"
    }
  ];

  // Featured images data
  const featuredImages = [
    {
      id: 1,
      image: "/hero.jpeg",
      title: "Stadium View",
      description: "Our magnificent home ground"
    },
    {
      id: 2,
      image: "/1.jpeg",
      title: "Team Spirit",
      description: "Unity and determination"
    },
    {
      id: 3,
      image: "/emmanuel.jpeg",
      title: "Star Player",
      description: "Emmanuel Toy<PERSON>"
    },
    {
      id: 4,
      image: "/Crown_F.C_Nigeria_logo.svg.png",
      title: "Club Heritage",
      description: "Our proud legacy since 1994"
    }
  ];

  // Auto-advance carousel
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
    }, 5000);
    return () => clearInterval(timer);
  }, [heroSlides.length]);

  return (
    <>
      <Header />
      <div className="min-h-screen">

        {/* Hero Carousel Section */}
        <div className="relative h-[80vh] overflow-hidden">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentSlide}
              initial={{ opacity: 0, x: 300 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -300 }}
              transition={{ duration: 0.8, ease: "easeInOut" }}
              className="absolute inset-0"
            >
              <div
                className="w-full h-full bg-cover bg-center bg-no-repeat"
                style={{ backgroundImage: `url(${heroSlides[currentSlide].image})` }}
              >
                <div className="absolute inset-0 bg-black/70"></div>
                <div className="container mx-auto px-4 h-full flex items-center">
                  <div className="text-white space-y-6 max-w-3xl">
                    <h1 className="text-3xl md:text-7xl font-bold leading-tight text-white drop-shadow-lg">
                      {heroSlides[currentSlide].title}
                    </h1>
                    <div className="inline-block">
                      <h2 className="text-xl md:text-4xl font-semibold text-white drop-shadow-lg bg-orange-500 px-4 py-2 rounded-lg">
                        {heroSlides[currentSlide].subtitle}
                      </h2>
                    </div>
                    <p className="text-lg md:text-2xl text-white drop-shadow-lg">
                      {heroSlides[currentSlide].description}
                    </p>

                  </div>
                </div>
              </div>
            </motion.div>
          </AnimatePresence>

          {/* Carousel Controls */}
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-2">
            {heroSlides.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentSlide ? 'bg-yellow-500 scale-125' : 'bg-white/50'
                }`}
              />
            ))}
          </div>

          {/* Navigation Arrows */}
          <button
            onClick={() => setCurrentSlide((prev) => (prev - 1 + heroSlides.length) % heroSlides.length)}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-2 rounded-full transition-all duration-300"
          >
            <ChevronLeft className="h-6 w-6" />
          </button>
          <button
            onClick={() => setCurrentSlide((prev) => (prev + 1) % heroSlides.length)}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-2 rounded-full transition-all duration-300"
          >
            <ChevronRight className="h-6 w-6" />
          </button>
        </div>

        {/* About Us Section */}
        <div className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-12"
            >
              <h2 className="text-2xl md:text-4xl font-bold text-gray-900 mb-4">About Crown Football Club</h2>
              <p className="text-base md:text-lg text-gray-600 max-w-3xl mx-auto">
                Founded in 1994 by Rev. Prof. Yusuf Ameh Obaje, Crown Football Club Ogbomoso has been a cornerstone of Nigerian football excellence for over three decades.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <motion.div
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <Trophy className="h-8 w-8" />
                </div>
                <h3 className="text-lg md:text-xl font-semibold text-gray-900 mb-2">Our Legacy</h3>
                <p className="text-sm md:text-base text-gray-600">
                  Competing in the Nigeria National League with pride and determination since our founding in 1994.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="bg-yellow-500 text-black rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <Users className="h-8 w-8" />
                </div>
                <h3 className="text-lg md:text-xl font-semibold text-gray-900 mb-2">Our Community</h3>
                <p className="text-sm md:text-base text-gray-600">
                  Based in Ogbomoso, Oyo State, we play at the iconic Soun Stadium Ogbomosho with a capacity of 7,200 passionate fans.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="bg-green-600 text-white rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <Target className="h-8 w-8" />
                </div>
                <h3 className="text-lg md:text-xl font-semibold text-gray-900 mb-2">Our Mission</h3>
                <p className="text-sm md:text-base text-gray-600">
                  To develop young talent, compete at the highest level, and bring glory to Ogbomoso and Nigerian football.
                </p>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Featured Images Section */}
        <div className="py-16 bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-100/40 backdrop-blur-sm">
          <div className="container mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-12 bg-gradient-to-r from-tm-navy via-tm-blue to-tm-navy text-white p-8 rounded-xl shadow-2xl relative overflow-hidden"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-tm-navy/90 via-tm-blue/80 to-tm-navy/90"></div>
              <div className="absolute inset-0 opacity-20">
                <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-white/5"></div>
                <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_30%_20%,rgba(255,255,255,0.1),transparent_50%)]"></div>
              </div>
              <div className="relative z-10">
                <h2 className="text-2xl md:text-4xl font-bold mb-4 flex items-center justify-center gap-3">
                  <span className="w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></span>
                  Featured Gallery
                  <span className="text-lg font-normal opacity-80 ml-2">(Latest Moments)</span>
                </h2>
                <p className="text-base md:text-lg text-white/90 max-w-2xl mx-auto">
                  Explore the moments that define Crown Football Club - from our magnificent stadium to our talented players.
                </p>
              </div>
            </motion.div>

            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
              {featuredImages.map((item, index) => (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ y: -10, scale: 1.02 }}
                  className="group"
                >
                  <Card className="overflow-hidden shadow-2xl bg-gradient-to-br from-white/80 via-slate-50/60 to-blue-50/40 backdrop-blur-sm hover:shadow-2xl hover:scale-105 transition-all duration-300 border border-tm-blue/20">
                    <div className="relative overflow-hidden">
                      <motion.img
                        src={item.image}
                        alt={item.title}
                        className="w-full h-48 object-cover"
                        whileHover={{ scale: 1.1 }}
                        transition={{ duration: 0.3 }}
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-tm-navy/60 via-transparent to-transparent group-hover:from-tm-navy/80 transition-all duration-300"></div>
                      <div className="absolute bottom-2 left-2">
                        <span className="w-2 h-2 bg-green-400 rounded-full animate-pulse inline-block"></span>
                      </div>
                    </div>
                    <CardContent className="p-4 bg-gradient-to-br from-white/90 via-slate-50/70 to-blue-50/50">
                      <h3 className="text-base md:text-lg font-semibold text-tm-navy mb-2 group-hover:text-tm-blue transition-colors">
                        {item.title}
                      </h3>
                      <p className="text-tm-dark-gray text-xs md:text-sm">
                        {item.description}
                      </p>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </div>

      </div>
      <Footer />
    </>
  );
};

export default Index;
