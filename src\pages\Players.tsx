import Header from "@/components/Header";
import Footer from "@/components/Footer";
import Breadcrumb from "@/components/Breadcrumb";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { User, Trophy, Target, Clock, ChevronLeft, ChevronRight } from "lucide-react";
import player<PERSON>hoto from "@/assets/player-photo.jpg";
import { useState, useRef } from "react";

const Players = () => {
  const [showAll, setShowAll] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const players = [
    {
      id: 1,
      name: "<PERSON>",
      position: "Attacking midfielder",
      number: 17,
      age: 26,
      height: "1.83m",
      photo: player<PERSON><PERSON><PERSON>,
      goals: 8,
      assists: 3,
      matches: 15
    },
    {
      id: 2,
      name: "<PERSON>",
      position: "Midfielder",
      number: 10,
      age: 24,
      height: "1.78m",
      photo: "/placeholder-player.jpg",
      goals: 4,
      assists: 7,
      matches: 18
    },
    {
      id: 3,
      name: "<PERSON>",
      position: "Defender",
      number: 5,
      age: 28,
      height: "1.85m",
      photo: "/placeholder-player.jpg",
      goals: 1,
      assists: 2,
      matches: 20
    },
    {
      id: 4,
      name: "David <PERSON>",
      position: "Goalkeeper",
      number: 1,
      age: 30,
      height: "1.88m",
      photo: "/placeholder-player.jpg",
      goals: 0,
      assists: 0,
      matches: 22
    },
    {
      id: 5,
      name: "Michael Brown",
      position: "Forward",
      number: 9,
      age: 25,
      height: "1.80m",
      photo: "/placeholder-player.jpg",
      goals: 12,
      assists: 5,
      matches: 20
    },
    {
      id: 6,
      name: "James Rodriguez",
      position: "Defender",
      number: 3,
      age: 27,
      height: "1.82m",
      photo: "/placeholder-player.jpg",
      goals: 2,
      assists: 1,
      matches: 19
    },
    {
      id: 7,
      name: "Samuel Okafor",
      position: "Midfielder",
      number: 8,
      age: 23,
      height: "1.75m",
      photo: "/placeholder-player.jpg",
      goals: 6,
      assists: 9,
      matches: 21
    },
    {
      id: 8,
      name: "Peter Adebayo",
      position: "Forward",
      number: 11,
      age: 24,
      height: "1.78m",
      photo: "/placeholder-player.jpg",
      goals: 10,
      assists: 4,
      matches: 18
    },
    {
      id: 9,
      name: "John Okwu",
      position: "Defender",
      number: 4,
      age: 29,
      height: "1.86m",
      photo: "/placeholder-player.jpg",
      goals: 1,
      assists: 0,
      matches: 22
    },
    {
      id: 10,
      name: "Daniel Eze",
      position: "Goalkeeper",
      number: 12,
      age: 26,
      height: "1.90m",
      photo: "/placeholder-player.jpg",
      goals: 0,
      assists: 0,
      matches: 8
    },
    {
      id: 11,
      name: "Victor Osimhen",
      position: "Forward",
      number: 14,
      age: 22,
      height: "1.85m",
      photo: "/placeholder-player.jpg",
      goals: 15,
      assists: 2,
      matches: 19
    },
    {
      id: 12,
      name: "Kelechi Iheanacho",
      position: "Forward",
      number: 18,
      age: 25,
      height: "1.83m",
      photo: "/placeholder-player.jpg",
      goals: 9,
      assists: 6,
      matches: 17
    },
    {
      id: 13,
      name: "Wilfred Ndidi",
      position: "Midfielder",
      number: 6,
      age: 26,
      height: "1.82m",
      photo: "/placeholder-player.jpg",
      goals: 3,
      assists: 4,
      matches: 20
    },
    {
      id: 14,
      name: "Ahmed Musa",
      position: "Forward",
      number: 7,
      age: 29,
      height: "1.70m",
      photo: "/placeholder-player.jpg",
      goals: 7,
      assists: 8,
      matches: 16
    },
    {
      id: 15,
      name: "Kenneth Omeruo",
      position: "Defender",
      number: 2,
      age: 28,
      height: "1.84m",
      photo: "/placeholder-player.jpg",
      goals: 0,
      assists: 1,
      matches: 21
    },
    {
      id: 16,
      name: "Francis Uzoho",
      position: "Goalkeeper",
      number: 23,
      age: 24,
      height: "1.96m",
      photo: "/placeholder-player.jpg",
      goals: 0,
      assists: 0,
      matches: 12
    },
    {
      id: 17,
      name: "Chidozie Awaziem",
      position: "Defender",
      number: 21,
      age: 25,
      height: "1.88m",
      photo: "/placeholder-player.jpg",
      goals: 1,
      assists: 2,
      matches: 18
    },
    {
      id: 18,
      name: "Joe Aribo",
      position: "Midfielder",
      number: 13,
      age: 26,
      height: "1.85m",
      photo: "/placeholder-player.jpg",
      goals: 5,
      assists: 7,
      matches: 19
    },
    {
      id: 19,
      name: "Moses Simon",
      position: "Forward",
      number: 15,
      age: 27,
      height: "1.68m",
      photo: "/placeholder-player.jpg",
      goals: 8,
      assists: 11,
      matches: 20
    },
    {
      id: 20,
      name: "Ola Aina",
      position: "Defender",
      number: 20,
      age: 26,
      height: "1.80m",
      photo: "/placeholder-player.jpg",
      goals: 2,
      assists: 5,
      matches: 17
    },
    {
      id: 21,
      name: "Taiwo Awoniyi",
      position: "Forward",
      number: 19,
      age: 25,
      height: "1.83m",
      photo: "/placeholder-player.jpg",
      goals: 11,
      assists: 3,
      matches: 18
    },
    {
      id: 22,
      name: "Calvin Bassey",
      position: "Defender",
      number: 22,
      age: 23,
      height: "1.84m",
      photo: "/placeholder-player.jpg",
      goals: 1,
      assists: 3,
      matches: 16
    },
    {
      id: 23,
      name: "Frank Onyeka",
      position: "Midfielder",
      number: 16,
      age: 24,
      height: "1.88m",
      photo: "/placeholder-player.jpg",
      goals: 2,
      assists: 2,
      matches: 15
    },
    {
      id: 24,
      name: "Innocent Bonke",
      position: "Midfielder",
      number: 24,
      age: 26,
      height: "1.75m",
      photo: "/placeholder-player.jpg",
      goals: 1,
      assists: 6,
      matches: 19
    },
    {
      id: 25,
      name: "Zaidu Sanusi",
      position: "Defender",
      number: 25,
      age: 25,
      height: "1.78m",
      photo: "/placeholder-player.jpg",
      goals: 0,
      assists: 4,
      matches: 14
    },
    {
      id: 26,
      name: "Tyronne Ebuehi",
      position: "Defender",
      number: 26,
      age: 27,
      height: "1.83m",
      photo: "/placeholder-player.jpg",
      goals: 1,
      assists: 2,
      matches: 13
    },
    {
      id: 27,
      name: "Paul Onuachu",
      position: "Forward",
      number: 27,
      age: 29,
      height: "2.01m",
      photo: "/placeholder-player.jpg",
      goals: 13,
      assists: 1,
      matches: 16
    },
    {
      id: 28,
      name: "Henry Onyekuru",
      position: "Forward",
      number: 28,
      age: 26,
      height: "1.77m",
      photo: "/placeholder-player.jpg",
      goals: 6,
      assists: 9,
      matches: 15
    },
    {
      id: 29,
      name: "Shehu Abdullahi",
      position: "Defender",
      number: 29,
      age: 30,
      height: "1.81m",
      photo: "/placeholder-player.jpg",
      goals: 0,
      assists: 1,
      matches: 12
    },
    {
      id: 30,
      name: "Oghenekaro Etebo",
      position: "Midfielder",
      number: 30,
      age: 27,
      height: "1.82m",
      photo: "/placeholder-player.jpg",
      goals: 4,
      assists: 5,
      matches: 17
    },
    {
      id: 31,
      name: "Sadiq Umar",
      position: "Forward",
      number: 31,
      age: 26,
      height: "1.90m",
      photo: "/placeholder-player.jpg",
      goals: 14,
      assists: 2,
      matches: 18
    },
    {
      id: 32,
      name: "Semi Ajayi",
      position: "Defender",
      number: 32,
      age: 29,
      height: "1.91m",
      photo: "/placeholder-player.jpg",
      goals: 3,
      assists: 1,
      matches: 20
    },
    {
      id: 33,
      name: "Terem Moffi",
      position: "Forward",
      number: 33,
      age: 24,
      height: "1.83m",
      photo: "/placeholder-player.jpg",
      goals: 12,
      assists: 4,
      matches: 19
    },
    {
      id: 34,
      name: "Raphael Onyedika",
      position: "Midfielder",
      number: 34,
      age: 22,
      height: "1.86m",
      photo: "/placeholder-player.jpg",
      goals: 2,
      assists: 3,
      matches: 14
    },
    {
      id: 35,
      name: "Bright Osayi-Samuel",
      position: "Defender",
      number: 35,
      age: 25,
      height: "1.75m",
      photo: "/placeholder-player.jpg",
      goals: 1,
      assists: 6,
      matches: 16
    },
    {
      id: 36,
      name: "Cyriel Dessers",
      position: "Forward",
      number: 36,
      age: 28,
      height: "1.90m",
      photo: "/placeholder-player.jpg",
      goals: 10,
      assists: 3,
      matches: 17
    },
    {
      id: 37,
      name: "Fisayo Dele-Bashiru",
      position: "Midfielder",
      number: 37,
      age: 23,
      height: "1.80m",
      photo: "/placeholder-player.jpg",
      goals: 3,
      assists: 8,
      matches: 15
    },
    {
      id: 38,
      name: "Bruno Onyemaechi",
      position: "Defender",
      number: 38,
      age: 24,
      height: "1.79m",
      photo: "/placeholder-player.jpg",
      goals: 0,
      assists: 3,
      matches: 11
    },
    {
      id: 39,
      name: "Akinkunmi Amoo",
      position: "Forward",
      number: 39,
      age: 21,
      height: "1.73m",
      photo: "/placeholder-player.jpg",
      goals: 4,
      assists: 7,
      matches: 13
    },
    {
      id: 40,
      name: "Nathan Tella",
      position: "Forward",
      number: 40,
      age: 24,
      height: "1.78m",
      photo: "/placeholder-player.jpg",
      goals: 5,
      assists: 4,
      matches: 12
    }
  ];

  const displayedPlayersDesktop = showAll ? players : players.slice(0, 6);
  const displayedPlayersMobile = players; // Always show all players on mobile
  const cardsPerView = 2; // Show 2 cards at a time on mobile
  const totalCardsMobile = displayedPlayersMobile.length;
  const maxIndexMobile = Math.max(0, totalCardsMobile - cardsPerView);

  const handleSwipeLeft = () => {
    setCurrentIndex(prev => Math.min(prev + 1, maxIndexMobile));
  };

  const handleSwipeRight = () => {
    setCurrentIndex(prev => Math.max(prev - 1, 0));
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    const touch = e.touches[0];
    scrollContainerRef.current?.setAttribute('data-start-x', touch.clientX.toString());
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    const touch = e.changedTouches[0];
    const startX = parseFloat(scrollContainerRef.current?.getAttribute('data-start-x') || '0');
    const endX = touch.clientX;
    const diff = startX - endX;

    // Minimum swipe distance to trigger action
    if (Math.abs(diff) > 50) {
      if (diff > 0) {
        handleSwipeLeft(); // Swipe left to show next cards
      } else {
        handleSwipeRight(); // Swipe right to show previous cards
      }
    }
  };

  return (
    <>
      <Header />
      <Breadcrumb 
        items={[
          { label: "Home", href: "/" },
          { label: "Player Profiles", href: "/players" }
        ]}
      />
      
      <div className="container mx-auto px-4 py-6">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-2xl md:text-3xl font-bold text-tm-navy mb-8">Player Profiles</h1>
          
          {/* Squad Overview */}
          <Card className="mb-8">
            <CardHeader className="bg-tm-navy text-tm-white">
              <h2 className="font-bold">Squad Overview</h2>
            </CardHeader>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="bg-tm-gray rounded-lg p-4 text-center">
                  <User className="h-6 w-6 mx-auto mb-2 text-tm-blue" />
                  <div className="text-xl font-bold text-tm-navy">40</div>
                  <div className="text-sm text-tm-dark-gray">Total Players</div>
                </div>
                <div className="bg-tm-gray rounded-lg p-4 text-center">
                  <Trophy className="h-6 w-6 mx-auto mb-2 text-tm-blue" />
                  <div className="text-xl font-bold text-tm-navy">648</div>
                  <div className="text-sm text-tm-dark-gray">Goals Scored</div>
                </div>
                <div className="bg-tm-gray rounded-lg p-4 text-center">
                  <Target className="h-6 w-6 mx-auto mb-2 text-tm-blue" />
                  <div className="text-xl font-bold text-tm-navy">28</div>
                  <div className="text-sm text-tm-dark-gray">Assists</div>
                </div>
                <div className="bg-tm-gray rounded-lg p-4 text-center">
                  <Clock className="h-6 w-6 mx-auto mb-2 text-tm-blue" />
                  <div className="text-xl font-bold text-tm-navy">21</div>
                  <div className="text-sm text-tm-dark-gray">Avg Age</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* All Players Grid */}
          <Card>
            <CardHeader className="bg-tm-navy text-tm-white">
              <h2 className="font-bold">All Players</h2>
            </CardHeader>
            <CardContent className="p-6">
              {/* Mobile: Swipeable Cards */}
              <div className="md:hidden">
                <div
                  ref={scrollContainerRef}
                  className="relative overflow-hidden"
                  onTouchStart={handleTouchStart}
                  onTouchEnd={handleTouchEnd}
                >
                  <div
                    className="flex transition-transform duration-300 ease-in-out gap-4"
                    style={{
                      transform: `translateX(-${currentIndex * (100 / cardsPerView)}%)`,
                      width: `${(totalCardsMobile / cardsPerView) * 100}%`
                    }}
                  >
                    {displayedPlayersMobile.map((player) => (
                      <div
                        key={player.id}
                        className="border border-tm-gray rounded-lg p-3 hover:shadow-lg transition-shadow flex-shrink-0"
                        style={{ width: `${100 / totalCardsMobile}%` }}
                      >
                        <div className="flex flex-col items-center gap-2">
                          <div className="relative">
                            <div className="w-12 h-14 bg-tm-gray rounded-lg flex items-center justify-center">
                              <User className="h-6 w-6 text-tm-blue" />
                            </div>
                            <div className="absolute -top-1 -left-1 bg-tm-blue text-tm-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold">
                              {player.number}
                            </div>
                          </div>
                          <div className="text-center">
                            <h3 className="font-bold text-tm-navy text-xs mb-1 leading-tight">{player.name}</h3>
                            <Badge variant="secondary" className="bg-tm-light-blue text-tm-navy text-xs mb-2">
                              {player.position}
                            </Badge>
                            <div className="space-y-1 text-xs text-tm-dark-gray">
                              <div>Age: {player.age}</div>
                              <div>Height: {player.height}</div>
                            </div>
                          </div>
                          <div className="grid grid-cols-3 gap-1 text-center w-full">
                            <div className="bg-tm-gray rounded p-1">
                              <div className="text-xs font-bold text-tm-navy">{player.goals}</div>
                              <div className="text-xs text-tm-dark-gray">G</div>
                            </div>
                            <div className="bg-tm-gray rounded p-1">
                              <div className="text-xs font-bold text-tm-navy">{player.assists}</div>
                              <div className="text-xs text-tm-dark-gray">A</div>
                            </div>
                            <div className="bg-tm-gray rounded p-1">
                              <div className="text-xs font-bold text-tm-navy">{player.matches}</div>
                              <div className="text-xs text-tm-dark-gray">M</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Mobile Navigation Dots */}
                <div className="flex justify-center mt-4 space-x-2">
                  {Array.from({ length: maxIndexMobile + 1 }).map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentIndex(index)}
                      className={`w-2 h-2 rounded-full transition-colors ${
                        index === currentIndex ? 'bg-tm-blue' : 'bg-tm-gray'
                      }`}
                    />
                  ))}
                </div>

                {/* Mobile Swipe Navigation Buttons */}
                <div className="flex justify-between mt-4">
                  <Button
                    onClick={handleSwipeRight}
                    disabled={currentIndex === 0}
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1"
                  >
                    <ChevronLeft className="h-4 w-4" />
                    Previous
                  </Button>
                  <Button
                    onClick={handleSwipeLeft}
                    disabled={currentIndex === maxIndexMobile}
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1"
                  >
                    Next
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Desktop: Grid Layout */}
              <div className="hidden md:grid md:grid-cols-4 gap-6">
                {displayedPlayersDesktop.map((player) => (
                  <div key={player.id} className="border border-tm-gray rounded-lg p-4 hover:shadow-lg transition-shadow">
                    <div className="flex items-start gap-4">
                      <div className="relative">
                        <div className="w-16 h-20 bg-tm-gray rounded-lg flex items-center justify-center">
                          <User className="h-8 w-8 text-tm-blue" />
                        </div>
                        <div className="absolute -top-2 -left-2 bg-tm-blue text-tm-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">
                          {player.number}
                        </div>
                      </div>
                      <div className="flex-1">
                        <h3 className="font-bold text-tm-navy mb-1">{player.name}</h3>
                        <Badge variant="secondary" className="bg-tm-light-blue text-tm-navy text-xs mb-2">
                          {player.position}
                        </Badge>
                        <div className="space-y-1 text-xs text-tm-dark-gray">
                          <div>Age: {player.age}</div>
                          <div>Height: {player.height}</div>
                          <div>Matches: {player.matches}</div>
                        </div>
                      </div>
                    </div>
                    <div className="mt-4 grid grid-cols-3 gap-2 text-center">
                      <div className="bg-tm-gray rounded p-2">
                        <div className="text-sm font-bold text-tm-navy">{player.goals}</div>
                        <div className="text-xs text-tm-dark-gray">Goals</div>
                      </div>
                      <div className="bg-tm-gray rounded p-2">
                        <div className="text-sm font-bold text-tm-navy">{player.assists}</div>
                        <div className="text-xs text-tm-dark-gray">Assists</div>
                      </div>
                      <div className="bg-tm-gray rounded p-2">
                        <div className="text-sm font-bold text-tm-navy">{player.matches}</div>
                        <div className="text-xs text-tm-dark-gray">Matches</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* See More / Show Less Button - Desktop Only */}
              <div className="hidden md:block">
                {!showAll && players.length > 6 && (
                  <div className="text-center mt-6">
                    <Button
                      onClick={() => setShowAll(true)}
                      className="bg-tm-blue hover:bg-tm-navy text-white px-6 py-2"
                    >
                      See More Players ({players.length - 6} remaining)
                    </Button>
                  </div>
                )}

                {showAll && (
                  <div className="text-center mt-6">
                    <Button
                      onClick={() => setShowAll(false)}
                      variant="outline"
                      className="border-tm-blue text-tm-blue hover:bg-tm-blue hover:text-white px-6 py-2"
                    >
                      Show Less
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Footer />
    </>
  );
};

export default Players;