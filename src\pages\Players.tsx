import Header from "@/components/Header";
import Footer from "@/components/Footer";
import Breadcrumb from "@/components/Breadcrumb";
import PlayerProfile from "@/components/PlayerProfile";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { User, Trophy, Target, Clock } from "lucide-react";
import playerPhoto from "@/assets/player-photo.jpg";

const Players = () => {
  const players = [
    {
      id: 1,
      name: "EMMANUEL TOYIN ADERINOLA",
      position: "Attacking midfielder",
      number: 17,
      age: 26,
      height: "1.83m",
      photo: player<PERSON>hot<PERSON>,
      goals: 8,
      assists: 3,
      matches: 15
    },
    {
      id: 2,
      name: "<PERSON>",
      position: "Midfielder",
      number: 10,
      age: 24,
      height: "1.78m",
      photo: "/placeholder-player.jpg",
      goals: 4,
      assists: 7,
      matches: 18
    },
    {
      id: 3,
      name: "<PERSON>",
      position: "Defender",
      number: 5,
      age: 28,
      height: "1.85m",
      photo: "/placeholder-player.jpg",
      goals: 1,
      assists: 2,
      matches: 20
    },
    {
      id: 4,
      name: "<PERSON>",
      position: "Goalkeeper",
      number: 1,
      age: 30,
      height: "1.88m",
      photo: "/placeholder-player.jpg",
      goals: 0,
      assists: 0,
      matches: 22
    }
  ];

  return (
    <>
      <Header />
      <Breadcrumb 
        items={[
          { label: "Home", href: "/" },
          { label: "Player Profiles", href: "/players" }
        ]}
      />
      
      <div className="container mx-auto px-4 py-6">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-2xl md:text-3xl font-bold text-tm-navy mb-8">Player Profiles</h1>
          
          {/* Squad Overview */}
          <Card className="mb-8">
            <CardHeader className="bg-tm-navy text-tm-white">
              <h2 className="font-bold">Squad Overview</h2>
            </CardHeader>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="bg-tm-gray rounded-lg p-4 text-center">
                  <User className="h-6 w-6 mx-auto mb-2 text-tm-blue" />
                  <div className="text-xl font-bold text-tm-navy">40</div>
                  <div className="text-sm text-tm-dark-gray">Total Players</div>
                </div>
                <div className="bg-tm-gray rounded-lg p-4 text-center">
                  <Trophy className="h-6 w-6 mx-auto mb-2 text-tm-blue" />
                  <div className="text-xl font-bold text-tm-navy">648</div>
                  <div className="text-sm text-tm-dark-gray">Goals Scored</div>
                </div>
                <div className="bg-tm-gray rounded-lg p-4 text-center">
                  <Target className="h-6 w-6 mx-auto mb-2 text-tm-blue" />
                  <div className="text-xl font-bold text-tm-navy">28</div>
                  <div className="text-sm text-tm-dark-gray">Assists</div>
                </div>
                <div className="bg-tm-gray rounded-lg p-4 text-center">
                  <Clock className="h-6 w-6 mx-auto mb-2 text-tm-blue" />
                  <div className="text-xl font-bold text-tm-navy">21</div>
                  <div className="text-sm text-tm-dark-gray">Avg Age</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Featured Player */}
          <Card className="mb-8">
            <CardHeader className="bg-tm-blue text-tm-white">
              <h2 className="font-bold">Featured Player</h2>
            </CardHeader>
            <CardContent className="p-0">
              <PlayerProfile />
            </CardContent>
          </Card>

          {/* All Players Grid */}
          <Card>
            <CardHeader className="bg-tm-navy text-tm-white">
              <h2 className="font-bold">All Players</h2>
            </CardHeader>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {players.map((player) => (
                  <div key={player.id} className="border border-tm-gray rounded-lg p-4 hover:shadow-lg transition-shadow">
                    <div className="flex items-start gap-4">
                      <div className="relative">
                        <div className="w-16 h-20 bg-tm-gray rounded-lg flex items-center justify-center">
                          <User className="h-8 w-8 text-tm-blue" />
                        </div>
                        <div className="absolute -top-2 -left-2 bg-tm-blue text-tm-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">
                          {player.number}
                        </div>
                      </div>
                      <div className="flex-1">
                        <h3 className="font-bold text-tm-navy mb-1">{player.name}</h3>
                        <Badge variant="secondary" className="bg-tm-light-blue text-tm-navy text-xs mb-2">
                          {player.position}
                        </Badge>
                        <div className="space-y-1 text-xs text-tm-dark-gray">
                          <div>Age: {player.age}</div>
                          <div>Height: {player.height}</div>
                          <div>Matches: {player.matches}</div>
                        </div>
                      </div>
                    </div>
                    <div className="mt-4 grid grid-cols-3 gap-2 text-center">
                      <div className="bg-tm-gray rounded p-2">
                        <div className="text-sm font-bold text-tm-navy">{player.goals}</div>
                        <div className="text-xs text-tm-dark-gray">Goals</div>
                      </div>
                      <div className="bg-tm-gray rounded p-2">
                        <div className="text-sm font-bold text-tm-navy">{player.assists}</div>
                        <div className="text-xs text-tm-dark-gray">Assists</div>
                      </div>
                      <div className="bg-tm-gray rounded p-2">
                        <div className="text-sm font-bold text-tm-navy">{player.matches}</div>
                        <div className="text-xs text-tm-dark-gray">Matches</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Footer />
    </>
  );
};

export default Players;